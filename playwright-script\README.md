# JSON to Playwright Script Generator (New Approach)

This tool converts JSON files generated by QA UI Testing agents into executable Playwright automation scripts using a structured, two-step approach without AI self-healing.

## New Approach Overview

The new approach follows these steps:

1. **Load and Clean JSON**: Remove screenshot fields from the input JSON
2. **Generate Structured Steps**: Use OpenAI to convert JSON actions into structured Playwright steps (JSON format)
3. **Save Structured JSON**: Save the structured steps for review and debugging
4. **Compile JavaScript**: Programmatically compile the structured steps into a JavaScript file using a template
5. **Execute Script**: Run the generated Playwright script

## Key Benefits

- **Better Code Quality**: OpenAI focuses only on generating the best possible step code snippets
- **Consistent Formatting**: Template-based compilation ensures consistent script structure
- **No AI Self-Healing**: Removed complex self-healing logic for now (can be added later)
- **Debuggable**: Structured JSON can be reviewed and modified before compilation
- **Maintainable**: Clear separation between AI generation and programmatic compilation

## Installation

1. Install Node.js dependencies:
```bash
npm install
```

2. Install Python dependencies:
```bash
pip install openai python-dotenv
```

3. Set up your OpenAI API key in a `.env` file:
```
OPENAI_API_KEY=your_openai_api_key_here
```

## Usage

### Command Line

```bash
# Basic usage
python json_to_playwright.py input.json

# Specify output file
python json_to_playwright.py input.json --output my_test.js

# Generate script but don't run it
python json_to_playwright.py input.json --no-run

# Use specific API key
python json_to_playwright.py input.json --api-key your_key
```

### Programmatic Usage

```python
from json_to_playwright import JsonToPlaywrightConverter

converter = JsonToPlaywrightConverter()
script_path = converter.process_json_file("input.json")
```

## File Structure

- `json_to_playwright.py` - Main converter class
- `test_new_approach.py` - Test script for the new approach
- `json_files/` - Sample JSON files for testing
- `playwright_steps.json` - Generated structured steps (intermediate file)
- `generated_playwright_test.js` - Final compiled JavaScript test

## Generated Script Structure

The generated JavaScript follows this template:

```javascript
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

const { chromium } = require('playwright');

(async () => {
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();

    try {
        console.log('🚀 Starting Playwright test execution...');
        
        // Generated steps with error handling
        // Each step includes:
        // - Descriptive logging
        // - Try/catch error handling
        // - Proper assertions
        // - Wait between steps
        
        console.log('✅ Test completed successfully');
        process.exit(0);
    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    } finally {
        await browser.close();
    }
})();
```

## Testing

Run the test script to verify the new approach:

```bash
python test_new_approach.py
```

This will test the complete workflow using the sample Amazon JSON file.

## Troubleshooting

1. **OpenAI API Key Issues**: Make sure your API key is set in the `.env` file
2. **Node.js Not Found**: Ensure Node.js is installed and available in PATH
3. **Playwright Dependencies**: Run `npm install` to install required packages
4. **Syntax Errors in Generated JS**: The tool automatically escapes quotes and handles common syntax issues

## Future Enhancements

- Add support for more complex selectors
- Implement optional AI self-healing
- Add test result reporting
- Support for multiple browsers
- Integration with CI/CD pipelines
