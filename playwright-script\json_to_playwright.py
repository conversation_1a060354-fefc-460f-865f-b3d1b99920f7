#!/usr/bin/env python3
"""
JSON to Playwright Script Generator (New Approach)

This script takes a JSON file generated by a QA UI Testing agent and:
1. Loads and cleans the JSON file (removes screenshots)
2. Sends it to OpenAI to convert JSON steps into structured Playwright steps (JSON format)
3. Saves the structured JSON
4. Compiles the JSON steps into a JS file using a template (programmatically)
5. Runs the JS script

Usage:
    python json_to_playwright.py <json_file_path>
"""

import json
import os
import sys
import subprocess
from pathlib import Path
from typing import Dict, Any, List
from openai import OpenAI
import argparse

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️  python-dotenv not installed. Install it with: pip install python-dotenv")
    print("⚠️  Alternatively, set OPENAI_API_KEY as a system environment variable")


class JsonToPlaywrightConverter:
    def __init__(self, openai_api_key: str = None):
        """
        Initialize the converter with OpenAI API key.

        Args:
            openai_api_key: OpenAI API key. If None, will try to get from environment.
        """
        self.openai_api_key = openai_api_key or os.getenv('OPENAI_API_KEY')
        if not self.openai_api_key:
            print("❌ OpenAI API key not found!")
            print("   Make sure you have:")
            print("   1. A .env file in the same directory with OPENAI_API_KEY=your_key")
            print("   2. python-dotenv installed: pip install python-dotenv")
            print("   3. Or set OPENAI_API_KEY as a system environment variable")
            print("   4. Or pass the API key using --api-key argument")
            raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable or pass it directly.")

        self.client = OpenAI(api_key=self.openai_api_key)
        
    def remove_screenshots_from_json(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Remove screenshot fields from the JSON data recursively.
        
        Args:
            json_data: The JSON data to process
            
        Returns:
            JSON data with screenshot fields removed
        """
        if isinstance(json_data, dict):
            cleaned_data = {}
            for key, value in json_data.items():
                if key.lower() in ['screenshot', 'screenshots']:
                    continue  # Skip screenshot fields
                else:
                    cleaned_data[key] = self.remove_screenshots_from_json(value)
            return cleaned_data
        elif isinstance(json_data, list):
            return [self.remove_screenshots_from_json(item) for item in json_data]
        else:
            return json_data
    
    def load_and_clean_json(self, json_file_path: str) -> Dict[str, Any]:
        """
        Load JSON file and remove screenshot fields.
        
        Args:
            json_file_path: Path to the JSON file
            
        Returns:
            Cleaned JSON data
        """
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            cleaned_data = self.remove_screenshots_from_json(json_data)
            print(f"✓ Successfully loaded and cleaned JSON from: {json_file_path}")
            return cleaned_data
            
        except FileNotFoundError:
            raise FileNotFoundError(f"JSON file not found: {json_file_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON format: {e}")
    
    def generate_playwright_steps_json(self, cleaned_json: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate structured Playwright steps JSON using OpenAI based on the cleaned JSON data.

        Args:
            cleaned_json: The cleaned JSON data from QA agent

        Returns:
            Structured JSON with Playwright steps
        """
        prompt = self._create_openai_prompt_for_steps(cleaned_json)

        try:
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert Playwright automation engineer. Convert QA testing data into structured Playwright steps in JSON format."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,
                max_tokens=4000
            )

            steps_json_str = response.choices[0].message.content.strip()

            # Extract JSON from response if it's wrapped in markdown
            if "```json" in steps_json_str:
                start = steps_json_str.find("```json") + 7
                end = steps_json_str.find("```", start)
                steps_json_str = steps_json_str[start:end].strip()
            elif "```" in steps_json_str:
                start = steps_json_str.find("```") + 3
                end = steps_json_str.find("```", start)
                steps_json_str = steps_json_str[start:end].strip()

            steps_json = json.loads(steps_json_str)
            print("✓ Successfully generated Playwright steps JSON using OpenAI")
            return steps_json

        except Exception as e:
            raise Exception(f"Failed to generate Playwright steps JSON: {e}")

    def save_playwright_steps_json(self, steps_json: Dict[str, Any], output_path: str = None) -> str:
        """
        Save the generated Playwright steps JSON to a file.

        Args:
            steps_json: The generated Playwright steps JSON
            output_path: Optional path to save the JSON. If None, generates a default name.

        Returns:
            Path to the saved JSON file
        """
        if output_path is None:
            output_path = os.path.join(os.path.dirname(__file__), "playwright_steps.json")

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(steps_json, f, indent=2, ensure_ascii=False)

            print(f"✓ Playwright steps JSON saved to: {output_path}")
            return output_path

        except Exception as e:
            raise Exception(f"Failed to save Playwright steps JSON: {e}")

    def compile_js_from_json(self, steps_json: Dict[str, Any], output_path: str = None) -> str:
        """
        Compile the Playwright steps JSON into a JavaScript file using a template.
        This is done programmatically without AI involvement.

        Args:
            steps_json: The Playwright steps JSON
            output_path: Optional path for the output JS file

        Returns:
            Path to the generated JS file
        """
        if output_path is None:
            output_path = os.path.join(os.path.dirname(__file__), "generated_playwright_test.js")

        try:
            # Get the template
            template = self._get_playwright_template()

            # Generate step implementations
            step_implementations = self._generate_step_implementations(steps_json)

            # Replace placeholder in template
            js_content = template.replace("// STEPS_PLACEHOLDER", step_implementations)

            # Write to file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(js_content)

            print(f"✓ Playwright JS script compiled to: {output_path}")
            return output_path

        except Exception as e:
            raise Exception(f"Failed to compile JS from JSON: {e}")
    
    def _create_openai_prompt_for_steps(self, cleaned_json: Dict[str, Any]) -> str:
        """
        Create a prompt for OpenAI to generate structured Playwright steps in JSON format.

        Args:
            cleaned_json: The cleaned JSON data

        Returns:
            Formatted prompt string
        """
        json_str = json.dumps(cleaned_json, indent=2)

        prompt = f"""You are tasked with converting QA testing data into structured Playwright steps in JSON format.

**Context:**
The provided JSON contains the history of actions performed by a QA testing agent on a web application. Your job is to convert these actions into structured Playwright steps that can be programmatically compiled into a JavaScript test script.

**JSON Data:**
```json
{json_str}
```

**REQUIREMENTS:**

1. **OUTPUT FORMAT**: Return ONLY a JSON object with this structure:
```json
{{
  "steps": [
    {{
      "step_name": "Navigate to homepage",
      "step_description": "Navigate to the Amazon homepage",
      "playwright_code": "await page.goto('https://www.amazon.in', {{ waitUntil: 'domcontentloaded', timeout: 10000 }});",
      "log_message": "Navigating to Amazon homepage"
    }},
    {{
      "step_name": "Click search button",
      "step_description": "Click the search button to submit search",
      "playwright_code": "await page.locator('button[type=\"submit\"]').click();",
      "log_message": "Clicking search button"
    }}
  ]
}}
```

2. **PLAYWRIGHT CODE REQUIREMENTS**:
   - Use ONLY CSS selectors, never XPath
   - Use modern Playwright syntax with page.locator()
   - Include appropriate timeouts (3-5 seconds max)
   - Use proper await syntax
   - Handle popups with try/catch blocks where needed

3. **ACTION MAPPING**:
   - `go_to_url` → `page.goto(url, {{ waitUntil: 'domcontentloaded', timeout: 10000 }})`
   - `input_text` → `page.locator(selector).fill(text)`
   - `click_element_by_index` → Convert to robust CSS selectors
   - `wait` actions → `page.waitForTimeout(milliseconds)`

4. **SELECTOR STRATEGY**:
   - Prefer semantic selectors (button[type="submit"], input[type="email"])
   - Use attribute selectors over complex CSS paths
   - Avoid nth-child selectors when possible
   - Use text-based selectors for buttons/links when appropriate

5. **POPUP HANDLING**:
   - Wrap popup-prone actions in try/catch blocks
   - Example: `try {{ await page.locator('button').click(); }} catch (e) {{ console.log('Popup handled or not present'); }}`

**IMPORTANT NOTES:**
- Focus ONLY on converting the actions from the JSON into structured steps
- Each step should have clean, executable Playwright code
- Use descriptive step names and log messages
- Keep the code simple and focused on the specific action
- Don't include complex retry logic or AI healing (that will be handled by the template)

**OUTPUT INSTRUCTIONS:**
Return ONLY the JSON object with the steps array. Do not include any explanations or markdown formatting around the JSON."""
        return prompt

    def _get_playwright_template(self) -> str:
        """
        Get the Playwright JavaScript template with placeholders for steps.

        Returns:
            Template string with placeholder for steps
        """
        template = '''const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

const { chromium } = require('playwright');

(async () => {
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();

    try {
        console.log('🚀 Starting Playwright test execution...');

        // STEPS_PLACEHOLDER

        console.log('✅ Test completed successfully');
        process.exit(0);
    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    } finally {
        await browser.close();
    }
})();'''
        return template

    def _generate_step_implementations(self, steps_json: Dict[str, Any]) -> str:
        """
        Generate step implementations from the structured JSON.

        Args:
            steps_json: The structured Playwright steps JSON

        Returns:
            Generated step implementations as string
        """
        implementations = []

        if 'steps' not in steps_json:
            raise ValueError("Invalid steps JSON format: missing 'steps' key")

        for i, step in enumerate(steps_json['steps']):
            step_name = step.get('step_name', f'Step {i+1}')
            step_description = step.get('step_description', '')
            playwright_code = step.get('playwright_code', '')
            log_message = step.get('log_message', f'Executing {step_name}')

            # Escape quotes in strings to prevent JavaScript syntax errors
            step_name_escaped = step_name.replace("'", "\\'").replace('"', '\\"')
            step_description_escaped = step_description.replace("'", "\\'").replace('"', '\\"')
            log_message_escaped = log_message.replace("'", "\\'").replace('"', '\\"')

            # Generate step implementation with error handling and assertions
            step_impl = f'''
        // {step_description_escaped}
        console.log("🔄 {log_message_escaped}");
        try {{
            {playwright_code}
            console.log("✅ {step_name_escaped} completed successfully");
        }} catch (error) {{
            console.error("❌ {step_name_escaped} failed:", error.message);
            throw error;
        }}

        // Wait a moment between steps
        await page.waitForTimeout(1000);'''

            implementations.append(step_impl)

        return '\n'.join(implementations)

    def run_playwright_script(self, script_path: str) -> bool:
        """
        Execute the generated Node.js Playwright script.

        Args:
            script_path: Path to the Playwright script to execute

        Returns:
            True if script executed successfully, False otherwise
        """
        try:
            print(f"🚀 Running Node.js Playwright script: {script_path}")

            # Check if Node.js is available
            try:
                subprocess.run(["node", "--version"], check=True, capture_output=True, shell=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                print("❌ Node.js not found. Please install Node.js first.")
                return False

            # Run the script
            result = subprocess.run(
                ["node", script_path],
                capture_output=True,
                text=True,
                cwd=os.path.dirname(script_path),
                shell=True,
                timeout=300
            )

            if result.returncode == 0:
                print("✅ Playwright script executed successfully")
                if result.stdout:
                    print("Script output:")
                    print(result.stdout)
                return True
            else:
                print("❌ Playwright script execution failed")
                if result.stderr:
                    print("Error output:")
                    print(result.stderr)
                if result.stdout:
                    print("Standard output:")
                    print(result.stdout)
                return False

        except Exception as e:
            print(f"❌ Failed to run Playwright script: {e}")
            return False

    def process_json_file(self, json_file_path: str, output_script_path: str = None, run_script: bool = True) -> str:
        """
        Complete workflow: load JSON, generate structured steps, compile JS script, and optionally run it.

        Args:
            json_file_path: Path to the input JSON file
            output_script_path: Optional path for the output script
            run_script: Whether to run the generated script

        Returns:
            Path to the generated script file
        """
        print("🔄 Starting JSON to Playwright conversion process...")

        # Step 1: Load and clean JSON
        cleaned_json = self.load_and_clean_json(json_file_path)

        # Step 2: Generate structured Playwright steps JSON
        steps_json = self.generate_playwright_steps_json(cleaned_json)

        # Step 3: Save the structured JSON
        steps_json_path = os.path.join(os.path.dirname(__file__), "playwright_steps.json")
        self.save_playwright_steps_json(steps_json, steps_json_path)

        # Step 4: Compile JS script from JSON
        script_path = self.compile_js_from_json(steps_json, output_script_path)

        # Step 5: Optionally run the script
        if run_script:
            success = self.run_playwright_script(script_path)
            if success:
                print("🎉 Process completed successfully!")
            else:
                print("⚠️  Process completed but script execution failed")
        else:
            print("✓ Script generated successfully (not executed)")

        return script_path




def main():
    """
    Main function to handle command line arguments and execute the conversion.
    """
    parser = argparse.ArgumentParser(
        description="Convert QA agent JSON to structured Playwright steps, compile to JS script and run it",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python json_to_playwright.py input.json
  python json_to_playwright.py input.json --output my_test.js
  python json_to_playwright.py input.json --no-run
  python json_to_playwright.py input.json --api-key your_openai_key
        """
    )

    parser.add_argument(
        "json_file",
        help="Path to the JSON file generated by QA agent"
    )

    parser.add_argument(
        "--output", "-o",
        help="Output path for the generated Playwright script (default: generated_playwright_test.js)"
    )

    parser.add_argument(
        "--no-run",
        action="store_true",
        help="Generate script but don't run it"
    )

    parser.add_argument(
        "--api-key",
        help="OpenAI API key (can also be set via OPENAI_API_KEY environment variable)"
    )

    args = parser.parse_args()

    try:
        # Initialize converter
        converter = JsonToPlaywrightConverter(openai_api_key=args.api_key)

        # Process the JSON file
        script_path = converter.process_json_file(
            json_file_path=args.json_file,
            output_script_path=args.output,
            run_script=not args.no_run
        )

        print(f"\n📁 Generated script location: {script_path}")

    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()