#!/usr/bin/env python3
"""
JSON to Playwright Script Generator

This script takes a JSON file generated by a QA UI Testing agent, removes screenshot fields,
generates a Playwright script using OpenAI, and then runs the generated script.

Usage:
    python json_to_playwright.py <json_file_path>
"""

import json
import os
import sys
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, Any, List
import openai
from openai import OpenAI
import argparse

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️  python-dotenv not installed. Install it with: pip install python-dotenv")
    print("⚠️  Alternatively, set OPENAI_API_KEY as a system environment variable")


class JsonToPlaywrightConverter:
    def __init__(self, openai_api_key: str = None):
        """
        Initialize the converter with OpenAI API key.
        
        Args:
            openai_api_key: OpenAI API key. If None, will try to get from environment.
        """
        self.openai_api_key = openai_api_key or os.getenv('OPENAI_API_KEY')
        if not self.openai_api_key:
            print("❌ OpenAI API key not found!")
            print("   Make sure you have:")
            print("   1. A .env file in the same directory with OPENAI_API_KEY=your_key")
            print("   2. python-dotenv installed: pip install python-dotenv")
            print("   3. Or set OPENAI_API_KEY as a system environment variable")
            print("   4. Or pass the API key using --api-key argument")
            raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable or pass it directly.")
        
        self.client = OpenAI(api_key=self.openai_api_key)
        
    def remove_screenshots_from_json(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Remove screenshot fields from the JSON data recursively.
        
        Args:
            json_data: The JSON data to process
            
        Returns:
            JSON data with screenshot fields removed
        """
        if isinstance(json_data, dict):
            cleaned_data = {}
            for key, value in json_data.items():
                if key.lower() in ['screenshot', 'screenshots']:
                    continue  # Skip screenshot fields
                else:
                    cleaned_data[key] = self.remove_screenshots_from_json(value)
            return cleaned_data
        elif isinstance(json_data, list):
            return [self.remove_screenshots_from_json(item) for item in json_data]
        else:
            return json_data
    
    def load_and_clean_json(self, json_file_path: str) -> Dict[str, Any]:
        """
        Load JSON file and remove screenshot fields.
        
        Args:
            json_file_path: Path to the JSON file
            
        Returns:
            Cleaned JSON data
        """
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            cleaned_data = self.remove_screenshots_from_json(json_data)
            print(f"✓ Successfully loaded and cleaned JSON from: {json_file_path}")
            return cleaned_data
            
        except FileNotFoundError:
            raise FileNotFoundError(f"JSON file not found: {json_file_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON format: {e}")
    
    def generate_playwright_script(self, cleaned_json: Dict[str, Any]) -> str:
        """
        Generate Playwright script using OpenAI based on the cleaned JSON data.
        
        Args:
            cleaned_json: The cleaned JSON data from QA agent
            
        Returns:
            Generated Playwright script as string
        """
        prompt = self._create_openai_prompt(cleaned_json)
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert Playwright automation engineer. Generate clean, executable Playwright scripts based on QA testing data."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=0.1,
                max_tokens=4000
            )
            
            playwright_script = response.choices[0].message.content
            print("✓ Successfully generated Playwright script using OpenAI")

            # Validate and fix common selector issues
            validated_script = self._validate_and_fix_selectors(playwright_script)

            # Fix common problematic patterns
            robust_script = self._fix_common_issues(validated_script)
            return robust_script
            
        except Exception as e:
            raise Exception(f"Failed to generate Playwright script: {e}")

    def _validate_and_fix_selectors(self, script_content: str) -> str:
        """
        Validate and fix common selector issues in the generated script.

        Args:
            script_content: The generated script content

        Returns:
            Fixed script content
        """
        import re

        lines = script_content.split('\n')
        fixed_lines = []

        for line in lines:
            fixed_line = line

            # Check for XPath selectors being used as CSS selectors
            # Pattern: page.click("/html/body/...") or page.fill("/html/body/...")
            xpath_pattern = r'(page\.(click|fill|wait_for_selector))\s*\(\s*["\'](/html/[^"\']*)["\']'
            match = re.search(xpath_pattern, line)
            if match:
                method_call = match.group(1)
                xpath_selector = match.group(3)
                method_name = match.group(2)

                # Convert to locator syntax with proper method call
                if method_name in ['click']:
                    fixed_line = re.sub(
                        xpath_pattern,
                        rf'page.locator("xpath={xpath_selector}").{method_name}(',
                        line
                    )
                elif method_name in ['fill']:
                    # For fill, we need to preserve the second argument
                    fill_pattern = r'(page\.fill)\s*\(\s*["\'](/html/[^"\']*)["\'],\s*([^)]+)\)'
                    fill_match = re.search(fill_pattern, line)
                    if fill_match:
                        xpath_sel = fill_match.group(2)
                        fill_value = fill_match.group(3)
                        fixed_line = re.sub(
                            fill_pattern,
                            rf'page.locator("xpath={xpath_sel}").fill({fill_value})',
                            line
                        )
                    else:
                        fixed_line = re.sub(
                            xpath_pattern,
                            rf'page.locator("xpath={xpath_selector}").{method_name}(',
                            line
                        )
                else:
                    fixed_line = re.sub(
                        xpath_pattern,
                        rf'page.locator("xpath={xpath_selector}")',
                        line
                    )
                print(f"⚠️  Fixed XPath selector in line: {line.strip()}")

            # Check for invalid CSS selectors with @ symbols
            css_with_at_pattern = r'(page\.(click|fill|wait_for_selector))\s*\(\s*["\']([^"\']*@[^"\']*)["\']'
            match = re.search(css_with_at_pattern, line)
            if match:
                method_call = match.group(1)
                invalid_selector = match.group(3)

                # Try to convert common XPath patterns to CSS
                if '@type=' in invalid_selector:
                    # Convert @type="button" to [type="button"]
                    css_selector = re.sub(r'@type="([^"]*)"', r'[type="\1"]', invalid_selector)
                    css_selector = re.sub(r'^/html/body/', '', css_selector)  # Remove XPath prefix
                    css_selector = re.sub(r'/div\[(\d+)\]', r' > div:nth-child(\1)', css_selector)  # Convert div[1] to div:nth-child(1)

                    if css_selector != invalid_selector:
                        fixed_line = re.sub(
                            css_with_at_pattern,
                            rf'{method_call}("{css_selector}"',
                            line
                        )
                        print(f"⚠️  Fixed invalid CSS selector: {invalid_selector} → {css_selector}")
                    else:
                        # If conversion failed, use locator with xpath
                        method_name = match.group(2)
                        if method_name in ['click']:
                            fixed_line = re.sub(
                                css_with_at_pattern,
                                rf'page.locator("xpath={invalid_selector}").{method_name}(',
                                line
                            )
                        elif method_name in ['fill']:
                            # Handle fill method specially
                            fill_pattern = r'(page\.fill)\s*\(\s*["\']([^"\']*@[^"\']*)["\'],\s*([^)]+)\)'
                            fill_match = re.search(fill_pattern, line)
                            if fill_match:
                                xpath_sel = fill_match.group(2)
                                fill_value = fill_match.group(3)
                                fixed_line = re.sub(
                                    fill_pattern,
                                    rf'page.locator("xpath={xpath_sel}").fill({fill_value})',
                                    line
                                )
                            else:
                                fixed_line = re.sub(
                                    css_with_at_pattern,
                                    rf'page.locator("xpath={invalid_selector}").{method_name}(',
                                    line
                                )
                        else:
                            fixed_line = re.sub(
                                css_with_at_pattern,
                                rf'page.locator("xpath={invalid_selector}")',
                                line
                            )
                        print(f"⚠️  Converted to XPath locator: {invalid_selector}")

            # Check for mixed XPath/CSS syntax and fix
            mixed_pattern = r'(page\.(click|fill|wait_for_selector))\s*\(\s*["\']([^"\']*\[[^\]]*@[^\]]*\][^"\']*)["\']'
            match = re.search(mixed_pattern, line)
            if match:
                method_call = match.group(1)
                method_name = match.group(2)
                mixed_selector = match.group(3)

                # Convert to XPath locator with proper method call
                if method_name in ['click']:
                    fixed_line = re.sub(
                        mixed_pattern,
                        rf'page.locator("xpath={mixed_selector}").{method_name}(',
                        line
                    )
                elif method_name in ['fill']:
                    # Handle fill method specially
                    fill_pattern = r'(page\.fill)\s*\(\s*["\']([^"\']*\[[^\]]*@[^\]]*\][^"\']*)["\'],\s*([^)]+)\)'
                    fill_match = re.search(fill_pattern, line)
                    if fill_match:
                        xpath_sel = fill_match.group(2)
                        fill_value = fill_match.group(3)
                        fixed_line = re.sub(
                            fill_pattern,
                            rf'page.locator("xpath={xpath_sel}").fill({fill_value})',
                            line
                        )
                    else:
                        fixed_line = re.sub(
                            mixed_pattern,
                            rf'page.locator("xpath={mixed_selector}").{method_name}(',
                            line
                        )
                else:
                    fixed_line = re.sub(
                        mixed_pattern,
                        rf'page.locator("xpath={mixed_selector}")',
                        line
                    )
                print(f"⚠️  Fixed mixed selector syntax: {mixed_selector}")

            fixed_lines.append(fixed_line)

        return '\n'.join(fixed_lines)

    def _fix_common_issues(self, script_content: str) -> str:
        """
        Fix common problematic patterns in generated scripts.

        Args:
            script_content: The script content to fix

        Returns:
            Fixed script content
        """
        import re

        lines = script_content.split('\n')
        fixed_lines = []
        in_while_loop = False
        while_loop_indent = 0

        for i, line in enumerate(lines):
            fixed_line = line

            # Detect and fix infinite while loops
            if re.match(r'\s*while\s+True\s*:', line):
                in_while_loop = True
                while_loop_indent = len(line) - len(line.lstrip())
                print("⚠️  Found potentially infinite while loop - converting to bounded retry")
                # Convert to bounded for loop
                indent = ' ' * while_loop_indent
                fixed_line = f"{indent}for attempt in range(3):  # Bounded retry instead of infinite loop"

            elif in_while_loop and line.strip() == 'break' and len(line) - len(line.lstrip()) > while_loop_indent:
                # Keep the break statement as is (already handled in the loop conversion)
                pass

            elif in_while_loop and (len(line) - len(line.lstrip()) <= while_loop_indent) and line.strip():
                in_while_loop = False

            # Fix overly long timeouts
            timeout_pattern = r'timeout\s*=\s*(\d+)'
            match = re.search(timeout_pattern, line)
            if match:
                timeout_value = int(match.group(1))
                if timeout_value > 10000:  # More than 10 seconds
                    new_timeout = min(5000, timeout_value // 6)  # Reduce to max 5 seconds
                    fixed_line = re.sub(timeout_pattern, f'timeout={new_timeout}', line)
                    print(f"⚠️  Reduced timeout from {timeout_value}ms to {new_timeout}ms")

            # Fix networkidle timeouts specifically
            if 'wait_for_load_state' in line and 'networkidle' in line:
                if 'timeout=' not in line:
                    # Add reasonable timeout if none specified
                    fixed_line = line.replace("'networkidle')", "'networkidle', timeout=5000)")
                    print("⚠️  Added timeout to wait_for_load_state")
                elif re.search(r'timeout\s*=\s*(\d+)', line):
                    match = re.search(r'timeout\s*=\s*(\d+)', line)
                    if match and int(match.group(1)) > 10000:
                        fixed_line = re.sub(r'timeout\s*=\s*\d+', 'timeout=5000', line)
                        print("⚠️  Reduced networkidle timeout to 5 seconds")

            # Replace networkidle with domcontentloaded for better reliability
            if 'wait_for_load_state' in line and 'networkidle' in line:
                fixed_line = fixed_line.replace("'networkidle'", "'domcontentloaded'")
                print("⚠️  Changed networkidle to domcontentloaded for better reliability")

            # Add error handling to bare click/fill operations (skip for Node.js files)
            if re.match(r'\s*await\s+page\.(click|fill)', line) and 'try' not in lines[max(0, i-2):i+1]:
                # Check if this is a JavaScript file (skip Python-style error handling)
                if any('const {' in l or 'require(' in l for l in lines[:10]):
                    # This is a JavaScript file, don't add Python syntax
                    pass
                else:
                    # Check if this line is already in a try block
                    preceding_lines = lines[max(0, i-5):i]
                    in_try_block = any('try:' in pline and 'except' not in pline for pline in preceding_lines)

                    if not in_try_block:
                        indent = ' ' * (len(line) - len(line.lstrip()))
                        # Wrap in try-except (Python style)
                        fixed_lines.append(f"{indent}try:")
                        fixed_lines.append(f"{indent}    {line.strip()}")
                        fixed_lines.append(f"{indent}except Exception as e:")
                        fixed_lines.append(f"{indent}    logger.warning(f'Failed to interact with element: {{e}}')")
                        fixed_lines.append(f"{indent}    # Continue execution instead of failing")
                        continue

            fixed_lines.append(fixed_line)

        return '\n'.join(fixed_lines)
    
    def _create_openai_prompt(self, cleaned_json: Dict[str, Any]) -> str:
        """
        Create a detailed prompt for OpenAI to generate the Playwright script.

        Args:
            cleaned_json: The cleaned JSON data

        Returns:
            Formatted prompt string
        """
        json_str = json.dumps(cleaned_json, indent=2)

        prompt = f"""You are tasked with generating a Node.js Playwright automation script based on QA testing data from an AI agent.

**Context:**
The provided JSON contains the history of actions performed by a QA testing agent on a web application. Your job is to replicate these actions in a Playwright script that can be executed independently.

**JSON Data:**
```json
{json_str}
```

**CRITICAL REQUIREMENTS FOR ROBUST NODE.JS SCRIPTS WITH AI SELF-HEALING:**

1. **NODE.JS PLAYWRIGHT**: Generate Node.js/JavaScript code, NOT Python
2. **CSS SELECTORS ONLY**: Use ONLY CSS selectors, never XPath selectors
3. **AI SELF-HEALING**: Include retry tracking and AI-powered error analysis/fixing
4. **STEP RETRY TRACKING**: Track failed attempts for each step (max 3 retries)
5. **SCREENSHOT + DOM CAPTURE**: Capture screenshot and DOM on repeated failures
6. **OPENAI INTEGRATION**: Send failure data to OpenAI for error analysis and solution suggestions
7. **DYNAMIC SCRIPT UPDATING**: Allow runtime script modification for error fixes
8. **GENERALIZED FIXES**: Apply AI fixes for any type of UI issue (popups, overlays, navigation changes, etc.)
9. **FALLBACK STRATEGY**: Always provide multiple selector options with try/catch blocks
10. **NO INFINITE LOOPS**: Never create loops that can run indefinitely
11. **REALISTIC TIMEOUTS**: Use shorter timeouts (3-5 seconds) for most operations
12. **ADD VALIDATION**: Add validation steps after each key step is performed
13. **EXIT CODES**: Use process.exit(0) on success, process.exit(1) on failure

**MANDATORY CODE STRUCTURE TEMPLATE:**
```javascript
const path = require('path');
require('dotenv').config({{ path: path.resolve(__dirname, '.env') }});

const {{ chromium }} = require('playwright');
const OpenAI = require('openai');
const fs = require('fs');

const openai = new OpenAI({{
    apiKey: process.env.OPENAI_API_KEY
}});

const stepRetryCount = {{}};

// Helper function to try multiple selectors
async function tryMultipleSelectors(page, selectors, action = 'click', text = null) {{
    for (const selector of selectors) {{
        try {{
            const element = await page.locator(selector).first();
            if (await element.isVisible({{ timeout: 2000 }})) {{
                if (action === 'click') {{
                    await element.click({{ timeout: 3000 }});
                    return true;
                }} else if (action === 'fill' && text) {{
                    await element.fill(text);
                    return true;
                }} else if (action === 'type' && text) {{
                    await element.type(text);
                    return true;
                }}
            }}
        }} catch (err) {{
            console.log(`Selector failed: ${{selector}} - ${{err.message}}`);
            continue;
        }}
    }}
    return false;
}}

async function analyzeWithOpenAI(error, domContent, stepName, screenshotPath) {{
    const response = await openai.chat.completions.create({{
        model: "gpt-4o-mini",
        messages: [
            {{
                role: "system",
                content: "You are a UI automation expert. Analyze the error and DOM to suggest the next action. Return a JSON object with: {{\\\"issue_type\\\": \\\"popup|overlay|navigation|element_missing|other\\\", \\\"suggested_action\\\": \\\"click|wait|navigate|skip\\\", \\\"selector\\\": \\\"CSS_selector_or_null\\\", \\\"explanation\\\": \\\"brief_explanation\\\"}}"
            }},
            {{
                role: "user",
                content: `Step: ${{stepName}}\\nError: ${{error}}\\nDOM snippet: ${{domContent.substring(0, 2000)}}\\nScreenshot saved at: ${{screenshotPath}}`
            }}
        ],
        max_tokens: 200
    }});

    try {{
        return JSON.parse(response.choices[0].message.content.trim());
    }} catch {{
        return {{ issue_type: "other", suggested_action: "skip", selector: null, explanation: "Could not parse AI response" }};
    }}
}}

async function performStepWithRetries(stepName, stepFunction, page) {{
    const fs = require('fs');

    while (true) {{
        try {{
            await stepFunction();
            stepRetryCount[stepName] = 0;
            console.log(`✓ Step "${{stepName}}" completed successfully`);
            return;
        }} catch (err) {{
            stepRetryCount[stepName] = (stepRetryCount[stepName] || 0) + 1;
            console.log(`Step "${{stepName}}" failed attempt ${{stepRetryCount[stepName]}}: ${{err.message}}`);

            if (stepRetryCount[stepName] >= 3) {{
                console.log(`Step "${{stepName}}" failed after 3 attempts. Calling AI Self-Healing...`);

                // Capture screenshot and DOM for AI analysis
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const screenshotPath = `failure_${{stepName.replace(/[^a-zA-Z0-9]/g, '_')}}_${{timestamp}}.png`;
                const domPath = `failure_${{stepName.replace(/[^a-zA-Z0-9]/g, '_')}}_${{timestamp}}.html`;

                try {{
                    await page.screenshot({{ path: screenshotPath, fullPage: true }});
                    const domContent = await page.content();
                    fs.writeFileSync(domPath, domContent);

                    console.log(`Screenshot: ${{screenshotPath}}, DOM saved at: ${{domPath}}`);

                    // Get AI analysis
                    const aiAnalysis = await analyzeWithOpenAI(err.message, domContent, stepName, screenshotPath);
                    console.log(`AI Analysis: ${{JSON.stringify(aiAnalysis)}}`);

                    // Try to apply AI suggested fix
                    if (aiAnalysis.suggested_action === 'click' && aiAnalysis.selector) {{
                        try {{
                            console.log(`Trying AI suggested fix: clicking ${{aiAnalysis.selector}}`);
                            await page.click(aiAnalysis.selector, {{ timeout: 5000 }});
                            stepRetryCount[stepName] = 0; // Reset retry count after successful fix
                            await page.waitForTimeout(2000); // Wait for any transitions
                            continue; // Retry the original step
                        }} catch (fixErr) {{
                            console.log(`AI suggested fix failed: ${{fixErr.message}}`);
                        }}
                    }} else if (aiAnalysis.suggested_action === 'wait') {{
                        console.log(`AI suggests waiting...`);
                        await page.waitForTimeout(5000);
                        stepRetryCount[stepName] = 0;
                        continue;
                    }} else if (aiAnalysis.suggested_action === 'navigate') {{
                        console.log(`AI suggests navigation issue, continuing...`);
                        stepRetryCount[stepName] = 0;
                        return; // Skip this step
                    }} else if (aiAnalysis.suggested_action === 'skip') {{
                        console.log(`AI suggests skipping this step: ${{aiAnalysis.explanation}}`);
                        stepRetryCount[stepName] = 0;
                        return; // Skip this step
                    }}

                }} catch (analysisErr) {{
                    console.log(`AI analysis failed: ${{analysisErr.message}}`);
                }}

                console.error(`Step "${{stepName}}" failed after 3 attempts and AI healing. ${{aiAnalysis?.explanation || ''}}`);
                throw new Error(`Step failed: ${{stepName}}`);
            }}

            await page.waitForTimeout(2000); // Wait before retry
        }}
    }}
}}

(async () => {{
    const browser = await chromium.launch({{ headless: false }});
    const page = await browser.newPage();

    try {{
        // Generate steps based on JSON actions here

        console.log('Test completed successfully');
        process.exit(0);
    }} catch (error) {{
        console.error('Test failed:', error);
        process.exit(1);
    }} finally {{
        await browser.close();
    }}
}})();
```

**Action Mapping Requirements:**
- `go_to_url` → `page.goto(url, {{ waitUntil: 'domcontentloaded', timeout: 10000 }})`
- `input_text` → `page.fill(selector, text)` or `page.type(selector, text)`
- `click_element_by_index` → Convert to robust CSS selectors (see Index Conversion Strategy below)
- `wait` actions → `page.waitForTimeout()` or `page.waitForSelector()`
- Handle any other actions found in the JSON appropriately

**Index Conversion Strategy (CRITICAL FOR SUCCESS):**
When you see `click_element_by_index` with an index number, you MUST create multiple fallback selectors based on common UI patterns:

**Robust Selector Generation Rules:**
1. **Multiple Selectors**: Always provide 5-8 fallback selectors for each action
2. **Semantic Priority**: Use semantic selectors first (role, aria-label, type)
3. **Text Content**: Include text-based selectors for buttons/links
4. **Attribute Combinations**: Combine multiple attributes for specificity
5. **Flexible Matching**: Use partial matches (*=) for dynamic content

**Selector Strategy (CRITICAL):**
- NEVER use XPath syntax like `/html/body/div[@class="something"]`
- ALWAYS use CSS selectors like `div.something` or `button[type="submit"]`
- If XPath is absolutely necessary, use: `page.locator("xpath=//button[@type='submit']").click()`
- Use robust CSS selectors: id > class > attribute > tag
- Provide multiple fallback selectors with try/catch blocks
- Examples of CORRECT selectors:
  * `#submit-button` (ID)
  * `.login-form button` (class + tag)
  * `button[type="submit"]` (attribute)
  * `input[placeholder*="email"]` (partial attribute match)

**AI Self-Healing Implementation Requirements:**
- **RETRY TRACKING**: Track attempts for each step with stepRetryCount object
- **FAILURE DETECTION**: Detect when a step fails 3 times consecutively
- **SCREENSHOT CAPTURE**: Take screenshot on repeated failures for visual context
- **DOM EXTRACTION**: Extract page DOM content on repeated failures
- **OPENAI ANALYSIS**: Send screenshot path, DOM, and error to OpenAI for comprehensive analysis
- **ISSUE CLASSIFICATION**: Ask OpenAI to classify issue type (popup, overlay, navigation, element_missing, other)
- **DYNAMIC FIXING**: Apply AI-suggested fixes based on issue type and suggested action
- **MULTIPLE FIX TYPES**: Handle clicks, waits, navigation issues, and element changes
- **RESUME EXECUTION**: Continue script execution after applying fixes
- **GENERALIZED APPROACH**: Handle any type of UI issue, not just popups

**Critical Error Prevention Rules:**
- **NO INFINITE LOOPS**: Never create while loops or retry mechanisms that can run forever
- **BOUNDED RETRIES**: If retrying, use a maximum of 3 attempts with clear exit conditions
- **SHORTER TIMEOUTS**: Use 3-5 second timeouts instead of 30+ seconds
- **GRACEFUL DEGRADATION**: If an optional element isn't found, log and continue
- **ELEMENT VERIFICATION**: Always check if elements exist before interacting
- **CLEAR ERROR MESSAGES**: Provide specific error messages about what failed
- **AVOID BRITTLE SELECTORS**: Don't rely on complex nth-child or position-based selectors
- **HANDLE DYNAMIC CONTENT**: Account for elements that may appear/disappear
- **NODE.JS SYNTAX**: Use proper Node.js/JavaScript syntax, not Python
- **NEVER mix CSS and XPath syntax in the same selector**

**SELECTOR VALIDATION RULES:**
- If a selector starts with `/` it's XPath - convert to CSS or use `page.locator("xpath=...")`
- If a selector contains `@` it's likely XPath - convert to CSS attribute selector
- Always test selectors are valid CSS before using them
- Use `page.locator()` method for complex selections

**MANDATORY OUTPUT REQUIREMENTS:**
1. Generate ONLY Node.js/JavaScript code with AI Self-Healing system
2. Include stepRetryCount tracking for all major steps
3. Include analyzeWithOpenAI function for comprehensive error analysis
4. Include performStepWithRetries() function with AI healing logic
5. Wrap all major steps with performStepWithRetries()
6. Use proper error handling and screenshot/DOM capture
7. Apply AI fixes for any type of UI issue (generalized approach)
8. Include environment variable handling for OpenAI API key
9. Map JSON actions to Playwright actions within performStepWithRetries calls
10. End with process.exit(0) on success, process.exit(1) on failure

**Step Implementation Example:**
For each action in the JSON, generate code like this:

```javascript
// For click_element_by_index actions
await performStepWithRetries('Click profile dropdown button', async () => {{
    const selectors = [
        'button[aria-haspopup="true"]',
        'button[type="button"].profile-dropdown',
        'button[type="button"][aria-label*="profile"]',
        'button[type="button"][aria-expanded]',
        'div[role="navigation"] button[type="button"]',
        'button:has-text("▼")',
        'button:has-text("▾")',
        'button:has-text("Profile")',
        'button[type="button"]'
    ];

    const clicked = await tryMultipleSelectors(page, selectors, 'click');
    if (!clicked) {{
        throw new Error('None of the selectors matched or clickable for step "Click profile dropdown button": ' + selectors.join(', '));
    }}
}}, page);

// For input_text actions
await performStepWithRetries('Enter email', async () => {{
    const selectors = [
        'input[type="email"]',
        'input[placeholder*="email"]',
        'input[name*="email"]',
        'input[id*="email"]',
        '#email',
        '.email-input'
    ];

    const filled = await tryMultipleSelectors(page, selectors, 'fill', '<EMAIL>');
    if (!filled) {{
        throw new Error('Could not find email input field');
    }}
}}, page);
```

**Output Instructions:**
Generate ONLY the complete, executable Node.js Playwright script with AI Self-Healing. Do not include explanations or markdown formatting around the code. Start with const {{ chromium }} require and end with closing parentheses. Map JSON actions to Playwright actions and wrap each major step with performStepWithRetries(). Use the tryMultipleSelectors helper function for all element interactions."""
        return prompt

    def save_playwright_script(self, script_content: str, output_path: str = None) -> str:
        """
        Save the generated Playwright script to a file.

        Args:
            script_content: The generated Playwright script content
            output_path: Optional path to save the script. If None, generates a default name.

        Returns:
            Path to the saved script file
        """
        if output_path is None:
            output_path = os.path.join(os.path.dirname(__file__), "generated_playwright_test.js")

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(script_content)

            print(f"✓ Playwright script saved to: {output_path}")
            return output_path

        except Exception as e:
            raise Exception(f"Failed to save Playwright script: {e}")

    def run_playwright_script(self, script_path: str) -> bool:
        """
        Execute the generated Node.js Playwright script.

        Args:
            script_path: Path to the Playwright script to execute

        Returns:
            True if script executed successfully, False otherwise
        """
        try:
            print(f"🚀 Running Node.js Playwright script: {script_path}")

            # Check if Node.js is available - try different commands
            node_available = False
            node_commands = ["node", "node.exe", "nodejs"]

            for node_cmd in node_commands:
                try:
                    subprocess.run([node_cmd, "--version"], check=True, capture_output=True, shell=True)
                    node_available = True
                    break
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue

            if not node_available:
                print("❌ Node.js not found. Please install Node.js first.")
                print("💡 Make sure Node.js is installed and available in PATH")
                return False

            # Check if npm is available
            npm_available = False
            npm_commands = ["npm", "npm.exe"]

            for npm_cmd in npm_commands:
                try:
                    subprocess.run([npm_cmd, "--version"], check=True, capture_output=True, shell=True)
                    npm_available = True
                    break
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue

            if not npm_available:
                print("❌ npm not found. Please install npm first.")
                return False

            # Check if playwright is installed for Node.js
            script_dir = os.path.dirname(script_path)
            package_json_path = os.path.join(script_dir, "package.json")

            if not os.path.exists(package_json_path):
                print("⚠️  Setting up Node.js environment...")
                self._setup_nodejs_environment(script_dir)

            # Run the script - try different node commands
            result = None
            for node_cmd in node_commands:
                try:
                    result = subprocess.run([node_cmd, script_path], capture_output=True, text=True, cwd=script_dir, shell=True, timeout=300)
                    break
                except (FileNotFoundError, subprocess.TimeoutExpired):
                    continue

            if result is None:
                print("❌ Could not execute Node.js script")
                return False

            if result.returncode == 0:
                print("✅ Playwright script executed successfully")
                if result.stdout:
                    print("Script output:")
                    print(result.stdout)
                return True
            else:
                print("❌ Playwright script execution failed")
                if result.stderr:
                    print("Error output:")
                    print(result.stderr)
                if result.stdout:
                    print("Standard output:")
                    print(result.stdout)
                return False

        except Exception as e:
            print(f"❌ Failed to run Playwright script: {e}")
            return False

    def _setup_nodejs_environment(self, script_dir: str):
        """
        Set up Node.js environment with Playwright dependencies.

        Args:
            script_dir: Directory where the script is located
        """
        try:
            # Create package.json
            package_json = {
                "name": "playwright-qa-test",
                "version": "1.0.0",
                "description": "Generated Playwright QA test with AI Self-Healing",
                "main": "generated_playwright_test.js",
                "scripts": {
                    "test": "node generated_playwright_test.js"
                },
                "dependencies": {
                    "playwright": "^1.40.0",
                    "openai": "^4.0.0"
                }
            }

            package_json_path = os.path.join(script_dir, "package.json")
            with open(package_json_path, 'w', encoding='utf-8') as f:
                json.dump(package_json, f, indent=2)

            print("✓ Created package.json")

            # Install dependencies
            print("📦 Installing Playwright for Node.js...")
            result = subprocess.run(["npm", "install"], cwd=script_dir, capture_output=True, text=True)

            if result.returncode != 0:
                print("⚠️  npm install failed, trying with --force...")
                subprocess.run(["npm", "install", "--force"], cwd=script_dir, check=True)

            print("✓ Installed Playwright dependencies")

            # Install Playwright browsers
            print("🌐 Installing Playwright browsers...")
            subprocess.run(["npx", "playwright", "install"], cwd=script_dir, check=True)
            print("✓ Installed Playwright browsers")

        except Exception as e:
            print(f"⚠️  Failed to set up Node.js environment: {e}")
            print("Please run manually:")
            print(f"  cd {script_dir}")
            print("  npm init -y")
            print("  npm install playwright")
            print("  npx playwright install")

    def process_json_file(self, json_file_path: str, output_script_path: str = None, run_script: bool = True) -> str:
        """
        Complete workflow: load JSON, generate Playwright script, save it, and optionally run it.

        Args:
            json_file_path: Path to the input JSON file
            output_script_path: Optional path for the output script
            run_script: Whether to run the generated script

        Returns:
            Path to the generated script file
        """
        print("🔄 Starting JSON to Playwright conversion process...")

        # Step 1: Load and clean JSON
        cleaned_json = self.load_and_clean_json(json_file_path)

        # Step 2: Generate Playwright script
        playwright_script = self.generate_playwright_script(cleaned_json)

        # Step 3: Save the script
        script_path = self.save_playwright_script(playwright_script, output_script_path)

        # Step 4: Optionally run the script
        if run_script:
            success = self.run_playwright_script(script_path)
            if success:
                print("🎉 Process completed successfully!")
            else:
                print("⚠️  Process completed but script execution failed")
        else:
            print("✓ Script generated successfully (not executed)")

        return script_path


def main():
    """
    Main function to handle command line arguments and execute the conversion.
    """
    parser = argparse.ArgumentParser(
        description="Convert QA agent JSON to Playwright script and run it",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python json_to_playwright.py input.json
  python json_to_playwright.py input.json --output my_test.py
  python json_to_playwright.py input.json --no-run
  python json_to_playwright.py input.json --api-key your_openai_key
        """
    )

    parser.add_argument(
        "json_file",
        help="Path to the JSON file generated by QA agent"
    )

    parser.add_argument(
        "--output", "-o",
        help="Output path for the generated Playwright script (default: generated_playwright_test.py)"
    )

    parser.add_argument(
        "--no-run",
        action="store_true",
        help="Generate script but don't run it"
    )

    parser.add_argument(
        "--api-key",
        help="OpenAI API key (can also be set via OPENAI_API_KEY environment variable)"
    )

    args = parser.parse_args()

    try:
        # Initialize converter
        converter = JsonToPlaywrightConverter(openai_api_key=args.api_key)

        # Process the JSON file
        script_path = converter.process_json_file(
            json_file_path=args.json_file,
            output_script_path=args.output,
            run_script=not args.no_run
        )

        print(f"\n📁 Generated script location: {script_path}")

    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()