const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

const { chromium } = require('playwright');

// Helper function to validate step completion
async function validateStepCompletion(page, stepName, validationCode = null) {
    try {
        if (validationCode) {
            // Execute custom validation code
            const result = await page.evaluate(validationCode);
            if (result) {
                console.log(`✅ Validation passed for: ${stepName}`);
                return true;
            } else {
                console.log(`⚠️  Validation failed for: ${stepName}`);
                return false;
            }
        } else {
            // Default validation - check if page is responsive
            await page.evaluate(() => document.readyState);
            console.log(`✅ Basic validation passed for: ${stepName}`);
            return true;
        }
    } catch (error) {
        console.log(`❌ Validation error for ${stepName}:`, error.message);
        return false;
    }
}

// Helper function to wait for page stability
async function waitForPageStability(page, timeout = 3000) {
    try {
        // Wait for network to be idle
        await page.waitForLoadState('networkidle', { timeout });
        // Additional wait for any dynamic content
        await page.waitForTimeout(1000);
        console.log('📄 Page stability confirmed');
    } catch (error) {
        console.log('⚠️  Page stability timeout, continuing...');
    }
}

(async () => {
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();

    try {
        console.log('🚀 Starting Playwright test execution...');

        // Wait for initial page load
        await waitForPageStability(page);

        
        // 
        console.log("🔄 Navigated to https://www.mindler.com/partner/login");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            await page.goto('https://www.mindler.com/partner/login', { timeout: 5000 });
            console.log("✅ Navigate to login page executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Navigate to login page", () => {
                return document.readyState === 'complete' &&
                       window.location.href !== 'about:blank' &&
                       document.body !== null;
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Navigate to login page failed:", error.message);
            throw error;
        }

        // 
        console.log("🔄 Entered email and password, clicked Log In button");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            await page.fill('input[formcontrolname="email"]', '<EMAIL>', { timeout: 4000 });
await page.fill('input[formcontrolname="password"]', '123456', { timeout: 4000 });
await page.click('button[type="submit"]', { timeout: 4000 });
            console.log("✅ Enter email and password, then click Log In executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Enter email and password, then click Log In", () => {
                const inputs = document.querySelectorAll('input, textarea');
                return Array.from(inputs).some(input => input.value.length > 0);
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Enter email and password, then click Log In failed:", error.message);
            throw error;
        }

        // 
        console.log("🔄 Clicked on the \'Students\' tab");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            await page.click('app-sidebar div:nth-of-type(2) > div:nth-of-type(2) > div:nth-of-type(5)', { timeout: 4000 });
            console.log("✅ Click on the \'Students\' tab executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Click on the \'Students\' tab", () => {
                const inputs = document.querySelectorAll('input, textarea');
                return Array.from(inputs).some(input => input.value.length > 0);
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Click on the \'Students\' tab failed:", error.message);
            throw error;
        }

        // 
        console.log("🔄 Clicked on the \'Log Out\' button");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            await page.click('app-sidebar div:nth-of-type(2) > div:nth-of-type(2) > div:nth-of-type(11)', { timeout: 4000 });
            console.log("✅ Click on the \'Log Out\' button executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Click on the \'Log Out\' button", () => {
                const inputs = document.querySelectorAll('input, textarea');
                return Array.from(inputs).some(input => input.value.length > 0);
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Click on the \'Log Out\' button failed:", error.message);
            throw error;
        }

        // 
        console.log("🔄 Re-entered email and password, clicked Log In button again");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            await page.fill('input[formcontrolname="email"]', '<EMAIL>', { timeout: 4000 });
await page.fill('input[formcontrolname="password"]', '123456', { timeout: 4000 });
await page.click('button[type="submit"]', { timeout: 4000 });
            console.log("✅ Re-enter email and password, then click Log In again executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Re-enter email and password, then click Log In again", () => {
                const inputs = document.querySelectorAll('input, textarea');
                return Array.from(inputs).some(input => input.value.length > 0);
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Re-enter email and password, then click Log In again failed:", error.message);
            throw error;
        }

        // 
        console.log("🔄 Clicked on the \'Students\' tab again");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            await page.click('app-sidebar div:nth-of-type(2) > div:nth-of-type(2) > div:nth-of-type(5)', { timeout: 4000 });
            console.log("✅ Click on the \'Students\' tab again executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Click on the \'Students\' tab again", () => {
                const inputs = document.querySelectorAll('input, textarea');
                return Array.from(inputs).some(input => input.value.length > 0);
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Click on the \'Students\' tab again failed:", error.message);
            throw error;
        }

        // 
        console.log("🔄 Clicked on the \'Log Out\' button again");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            await page.click('app-sidebar div:nth-of-type(2) > div:nth-of-type(2) > div:nth-of-type(11)', { timeout: 4000 });
            console.log("✅ Click on the \'Log Out\' button again executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Click on the \'Log Out\' button again", () => {
                const inputs = document.querySelectorAll('input, textarea');
                return Array.from(inputs).some(input => input.value.length > 0);
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Click on the \'Log Out\' button again failed:", error.message);
            throw error;
        }

        // 
        console.log("🔄 Successfully logged in, navigated to \'Students\' tab, and logged out; task completed");

        // Wait for page stability before executing step
        await waitForPageStability(page);

        try {
            // Task complete - no further actions required
            console.log("✅ Complete the task after successful logout executed successfully");

            // Wait for any page changes to complete
            await page.waitForTimeout(2000);
            await waitForPageStability(page);

            // Validate step completion
            const validationPassed = await validateStepCompletion(page, "Complete the task after successful logout", () => {
                return document.readyState === 'complete' && document.body !== null;
            });
            if (!validationPassed) {
                console.log("⚠️  Step validation failed but continuing...");
            }

        } catch (error) {
            console.error("❌ Complete the task after successful logout failed:", error.message);
            throw error;
        }

        console.log('✅ Test completed successfully');
        process.exit(0);
    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    } finally {
        await browser.close();
    }
})();