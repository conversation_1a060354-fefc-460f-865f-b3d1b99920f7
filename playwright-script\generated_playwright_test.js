const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

const { chromium } = require('playwright');

(async () => {
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();

    try {
        console.log('🚀 Starting Playwright test execution...');

        
        // Navigate to the Mindler partner login page
        console.log("🔄 Navigating to Mindler partner login page");
        try {
            await page.goto('https://www.mindler.com/partner/login', { waitUntil: 'domcontentloaded', timeout: 10000 });
            console.log("✅ Navigate to login page completed successfully");
        } catch (error) {
            console.error("❌ Navigate to login page failed:", error.message);
            throw error;
        }

        // Wait a moment between steps
        await page.waitForTimeout(1000);

        // Input email address into the email field
        console.log("🔄 Inputting email address");
        try {
            await page.locator('input[formcontrolname="email"]').fill('<EMAIL>');
            console.log("✅ Input email completed successfully");
        } catch (error) {
            console.error("❌ Input email failed:", error.message);
            throw error;
        }

        // Wait a moment between steps
        await page.waitForTimeout(1000);

        // Input password into the password field
        console.log("🔄 Inputting password");
        try {
            await page.locator('input[formcontrolname="password"]').fill('123456');
            console.log("✅ Input password completed successfully");
        } catch (error) {
            console.error("❌ Input password failed:", error.message);
            throw error;
        }

        // Wait a moment between steps
        await page.waitForTimeout(1000);

        // Click the Log In button to submit the login form
        console.log("🔄 Clicking Log In button");
        try {
            try { await page.locator('button[type="submit"]').click(); } catch (e) { console.log('Popup handled or not present'); }
            console.log("✅ Click Log In button completed successfully");
        } catch (error) {
            console.error("❌ Click Log In button failed:", error.message);
            throw error;
        }

        // Wait a moment between steps
        await page.waitForTimeout(1000);

        // Click on the Students tab in the dashboard
        console.log("🔄 Clicking Students tab");
        try {
            try { await page.locator('div:has-text("Students")').click(); } catch (e) { console.log('Popup handled or not present'); }
            console.log("✅ Click Students tab completed successfully");
        } catch (error) {
            console.error("❌ Click Students tab failed:", error.message);
            throw error;
        }

        // Wait a moment between steps
        await page.waitForTimeout(1000);

        // Click on the Log Out button to log out
        console.log("🔄 Clicking Log Out button");
        try {
            try { await page.locator('div:has-text("Log Out")').click(); } catch (e) { console.log('Popup handled or not present'); }
            console.log("✅ Click Log Out button completed successfully");
        } catch (error) {
            console.error("❌ Click Log Out button failed:", error.message);
            throw error;
        }

        // Wait a moment between steps
        await page.waitForTimeout(1000);

        // Re-input email address into the email field after logout
        console.log("🔄 Re-inputting email address");
        try {
            await page.locator('input[formcontrolname="email"]').fill('<EMAIL>');
            console.log("✅ Re-input email completed successfully");
        } catch (error) {
            console.error("❌ Re-input email failed:", error.message);
            throw error;
        }

        // Wait a moment between steps
        await page.waitForTimeout(1000);

        // Re-input password into the password field after logout
        console.log("🔄 Re-inputting password");
        try {
            await page.locator('input[formcontrolname="password"]').fill('123456');
            console.log("✅ Re-input password completed successfully");
        } catch (error) {
            console.error("❌ Re-input password failed:", error.message);
            throw error;
        }

        // Wait a moment between steps
        await page.waitForTimeout(1000);

        // Re-click the Log In button to submit the login form again
        console.log("🔄 Re-clicking Log In button");
        try {
            try { await page.locator('button[type="submit"]').click(); } catch (e) { console.log('Popup handled or not present'); }
            console.log("✅ Re-click Log In button completed successfully");
        } catch (error) {
            console.error("❌ Re-click Log In button failed:", error.message);
            throw error;
        }

        // Wait a moment between steps
        await page.waitForTimeout(1000);

        // Click on the Students tab in the dashboard again
        console.log("🔄 Re-clicking Students tab");
        try {
            try { await page.locator('div:has-text("Students")').click(); } catch (e) { console.log('Popup handled or not present'); }
            console.log("✅ Click Students tab again completed successfully");
        } catch (error) {
            console.error("❌ Click Students tab again failed:", error.message);
            throw error;
        }

        // Wait a moment between steps
        await page.waitForTimeout(1000);

        // Click on the Log Out button to log out again
        console.log("🔄 Re-clicking Log Out button");
        try {
            try { await page.locator('div:has-text("Log Out")').click(); } catch (e) { console.log('Popup handled or not present'); }
            console.log("✅ Click Log Out button again completed successfully");
        } catch (error) {
            console.error("❌ Click Log Out button again failed:", error.message);
            throw error;
        }

        // Wait a moment between steps
        await page.waitForTimeout(1000);

        console.log('✅ Test completed successfully');
        process.exit(0);
    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    } finally {
        await browser.close();
    }
})();