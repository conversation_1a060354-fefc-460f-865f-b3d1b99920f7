{"name": "playwright-script", "version": "1.0.0", "description": "This tool converts JSON files generated by QA UI Testing agents into executable Playwright automation scripts.", "main": "generated_playwright_test.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"dotenv": "^17.0.1", "openai": "^5.8.2", "playwright": "^1.53.2"}}